{"code-interpreter-v1": {"name": "Python data analyst", "lib": ["python", "jup<PERSON><PERSON>", "numpy", "pandas", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seaborn", "plotly"], "file": "script.py", "instructions": "Create sophisticated data analysis scripts with beautiful visualizations. Focus on clear code structure and comprehensive data insights.", "port": null}, "nextjs-developer": {"name": "Next.js developer", "lib": ["nextjs@14.2.5", "typescript", "@types/node", "@types/react", "@types/react-dom", "postcss", "tailwindcss", "shadcn"], "file": "pages/index.tsx", "instructions": "Build modern, performant Next.js applications with elegant UI/UX. Utilize TailwindCSS and shadcn for polished interfaces. Focus on responsive design and component reusability.", "port": 3000}, "vue-developer": {"name": "Vue.js developer", "lib": ["vue@latest", "nuxt@3.13.0", "tailwindcss"], "file": "app.vue", "instructions": "Create elegant Vue.js applications with a focus on component composition and state management. Implement responsive layouts and smooth animations.", "port": 3000}, "streamlit-developer": {"name": "Streamlit developer", "lib": ["streamlit", "pandas", "numpy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "request", "seaborn", "plotly"], "file": "app.py", "instructions": "Design interactive data applications with rich visualizations and intuitive user flows. Focus on performance and user experience.", "port": 8501}, "gradio-developer": {"name": "Gradio developer", "lib": ["gradio", "pandas", "numpy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "request", "seaborn", "plotly"], "file": "app.py", "instructions": "Build ML-focused interfaces with emphasis on model interaction and result visualization. Create intuitive layouts for complex data presentation.", "port": 7860}}
import { Message } from '@/lib/messages'
import { FragmentSchema } from '@/lib/schema'
import { ExecutionResult } from '@/lib/types'
import { DeepPartial } from 'ai'
import { LoaderIcon, Terminal } from 'lucide-react'
import { useEffect } from 'react'

export function Chat({
  messages,
  isLoading,
  setCurrentPreview,
}: {
  messages: Message[]
  isLoading: boolean
  setCurrentPreview: (preview: {
    fragment: DeepPartial<FragmentSchema> | undefined
    result: ExecutionResult | undefined
  }) => void
}) {
  useEffect(() => {
    const chatContainer = document.getElementById('chat-container')
    if (chatContainer) {
      chatContainer.scrollTop = chatContainer.scrollHeight
    }
  }, [JSON.stringify(messages)])

  return (
    <div
      id="chat-container"
      className="flex flex-col pb-12 gap-2 overflow-y-auto max-h-full"
    >
      {messages.map((message: Message, index: number) => (
        <div
          className={`flex flex-col px-4 shadow-sm whitespace-pre-wrap ${message.role !== 'user' ? 'bg-gradient-to-br from-white to-gray-50 dark:from-gray-900/80 dark:to-gray-800/80 border border-gray-200 dark:border-gray-700 text-gray-900 dark:text-gray-100 py-4 rounded-2xl gap-4 w-full' : 'bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700/80 dark:to-gray-600/80 border border-gray-300 dark:border-gray-600 py-3 rounded-xl gap-2 w-fit ml-auto text-gray-900 dark:text-gray-100'} font-mono`}
          key={index}
        >
          {message.content.map((content, id) => {
            if (content.type === 'text') {
              return content.text
            }
            if (content.type === 'image') {
              return (
                <img
                  key={id}
                  src={content.image}
                  alt="fragment"
                  className="mr-2 inline-block w-12 h-12 object-cover rounded-lg bg-white mb-2"
                />
              )
            }
          })}
          {message.object && (
            <div
              onClick={() =>
                setCurrentPreview({
                  fragment: message.object,
                  result: message.result,
                })
              }
              className="group relative overflow-hidden bg-gradient-to-r from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 border border-gray-200 dark:border-gray-700 rounded-2xl p-6 select-none hover:shadow-xl hover:scale-[1.02] transition-all duration-300 cursor-pointer"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-gray-100/50 to-gray-200/50 dark:from-gray-700/50 dark:to-gray-600/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

              <div className="relative flex flex-col items-center text-center">
                <h3 className="font-bold text-lg text-gray-900 dark:text-white mb-3">
                  {message.object.title}
                </h3>

                <div className="flex items-center gap-2 text-gray-600 dark:text-gray-300">
                  <span className="text-2xl animate-pulse group-hover:animate-bounce">---&gt;</span>
                  <span className="font-semibold text-base group-hover:text-gray-900 dark:group-hover:text-white transition-colors duration-200">
                    Click to view code & preview
                  </span>
                  <span className="text-2xl animate-pulse group-hover:animate-bounce">&lt;---</span>
                </div>

                <div className="mt-3 flex gap-1">
                  <div className="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-pulse group-hover:bg-green-500 transition-colors duration-300"></div>
                  <div className="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-pulse group-hover:bg-green-500 transition-colors duration-300 delay-100"></div>
                  <div className="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-pulse group-hover:bg-green-500 transition-colors duration-300 delay-200"></div>
                </div>
              </div>

              <div className="absolute top-3 right-3 w-3 h-3 bg-green-500 rounded-full animate-ping group-hover:animate-pulse" />
            </div>
          )}
        </div>
      ))}
      {isLoading && (
        <div className="flex items-center gap-1 text-sm text-muted-foreground">
          <LoaderIcon strokeWidth={2} className="animate-spin w-4 h-4" />
          <span>Generating...</span>
        </div>
      )}
    </div>
  )
}
